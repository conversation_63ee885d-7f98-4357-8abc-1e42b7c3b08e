[package]
name = "poe-profit-ai"
version = "1.0.0"
description = "PoE Profit AI - Stand-Alone Desktop Edition"
authors = ["PoE Profit AI Team"]
license = "MIT"
repository = "https://github.com/yourusername/poe-profit-ai"
edition = "2021"

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
# Core Tauri
tauri = { version = "2.0", features = [] }

# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

[dev-dependencies]
tempfile = "3.8"
tokio-test = "0.4"

[features]
default = ["custom-protocol"]
custom-protocol = ["tauri/custom-protocol"]

# Optimize for release builds
[profile.release]
panic = "abort"
codegen-units = 1
lto = true
opt-level = "s"  # Optimize for size
strip = true     # Remove debug symbols

C:\Users\<USER>\Downloads\my poe dashboard\src-tauri\target\debug\deps\libpoe_profit_ai-ca7a96aa00b58800.rmeta: src\main.rs src\models.rs src\data\mod.rs src\data\database.rs src\data\fetcher.rs src\data\validator.rs src\commands\mod.rs src\commands\price_commands.rs src\commands\flip_commands.rs src\commands\portfolio_commands.rs src\commands\market_commands.rs src\commands\system_commands.rs src\commands\ai_commands.rs src\cache.rs src\scheduler.rs \\?\C:\Users\<USER>\Downloads\my\ poe\ dashboard\src-tauri\migrations\001_initial.sql C:\Users\<USER>\Downloads\my\ poe\ dashboard\src-tauri\target\debug\build\poe-profit-ai-fd4c648552382705\out/e5bdb002d92a5df7ce5819f6ed4a40ba9e876d66d7c0d0f51882b1566860a329

C:\Users\<USER>\Downloads\my poe dashboard\src-tauri\target\debug\deps\poe_profit_ai-ca7a96aa00b58800.d: src\main.rs src\models.rs src\data\mod.rs src\data\database.rs src\data\fetcher.rs src\data\validator.rs src\commands\mod.rs src\commands\price_commands.rs src\commands\flip_commands.rs src\commands\portfolio_commands.rs src\commands\market_commands.rs src\commands\system_commands.rs src\commands\ai_commands.rs src\cache.rs src\scheduler.rs \\?\C:\Users\<USER>\Downloads\my\ poe\ dashboard\src-tauri\migrations\001_initial.sql C:\Users\<USER>\Downloads\my\ poe\ dashboard\src-tauri\target\debug\build\poe-profit-ai-fd4c648552382705\out/e5bdb002d92a5df7ce5819f6ed4a40ba9e876d66d7c0d0f51882b1566860a329

src\main.rs:
src\models.rs:
src\data\mod.rs:
src\data\database.rs:
src\data\fetcher.rs:
src\data\validator.rs:
src\commands\mod.rs:
src\commands\price_commands.rs:
src\commands\flip_commands.rs:
src\commands\portfolio_commands.rs:
src\commands\market_commands.rs:
src\commands\system_commands.rs:
src\commands\ai_commands.rs:
src\cache.rs:
src\scheduler.rs:
\\?\C:\Users\<USER>\Downloads\my\ poe\ dashboard\src-tauri\migrations\001_initial.sql:
C:\Users\<USER>\Downloads\my\ poe\ dashboard\src-tauri\target\debug\build\poe-profit-ai-fd4c648552382705\out/e5bdb002d92a5df7ce5819f6ed4a40ba9e876d66d7c0d0f51882b1566860a329:

# env-dep:CARGO_PKG_AUTHORS=PoE Profit AI Team
# env-dep:CARGO_PKG_DESCRIPTION=PoE Profit AI - Stand-Alone Desktop Edition
# env-dep:CARGO_PKG_NAME=poe-profit-ai
# env-dep:CARGO_PKG_VERSION=1.0.0
# env-dep:OUT_DIR=C:\\Users\\<USER>\\Downloads\\my poe dashboard\\src-tauri\\target\\debug\\build\\poe-profit-ai-fd4c648552382705\\out

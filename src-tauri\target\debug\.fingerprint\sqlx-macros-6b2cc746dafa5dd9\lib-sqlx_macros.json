{"rustc": 10895048813736897673, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"postgres\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 2225463790103693989, "path": 9936676360200748266, "deps": [[996810380461694889, "sqlx_core", false, 18070660541545428214], [2713742371683562785, "syn", false, 17375468584397320090], [3060637413840920116, "proc_macro2", false, 11906168919180298248], [15733334431800349573, "sqlx_macros_core", false, 5902672836285265746], [17990358020177143287, "quote", false, 299949230227233179]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-6b2cc746dafa5dd9\\dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
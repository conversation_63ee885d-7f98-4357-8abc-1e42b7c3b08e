use sqlx::{SqlitePool, Row};
use std::path::Path;
use anyhow::Result;
use chrono::{DateTime, Utc};
use fuzzy_matcher::FuzzyMatcher;
use fuzzy_matcher::skim::SkimMatcherV2;

use crate::models::*;

pub struct DatabaseManager {
    pool: SqlitePool,
    fuzzy_matcher: SkimMatcherV2,
}

impl DatabaseManager {
    pub fn get_pool(&self) -> &SqlitePool {
        &self.pool
    }

    pub async fn new(database_path: &str) -> Result<Self> {
        // Ensure directory exists
        if let Some(parent) = Path::new(database_path).parent() {
            tokio::fs::create_dir_all(parent).await?;
        }
        
        let database_url = format!("sqlite:{}", database_path);
        let pool = SqlitePool::connect(&database_url).await?;
        
        // Run migrations
        sqlx::migrate!("./migrations").run(&pool).await?;
        
        // Enable WAL mode for better performance
        sqlx::query("PRAGMA journal_mode = WAL").execute(&pool).await?;
        sqlx::query("PRAGMA synchronous = NORMAL").execute(&pool).await?;
        sqlx::query("PRAGMA cache_size = 10000").execute(&pool).await?;
        sqlx::query("PRAGMA temp_store = memory").execute(&pool).await?;
        
        let fuzzy_matcher = SkimMatcherV2::default();
        
        Ok(Self {
            pool,
            fuzzy_matcher,
        })
    }
    
    // League operations
    pub async fn get_leagues(&self) -> Result<Vec<League>> {
        let leagues = sqlx::query_as::<_, League>(
            "SELECT * FROM leagues ORDER BY is_active DESC, name ASC"
        )
        .fetch_all(&self.pool)
        .await?;
        
        Ok(leagues)
    }
    
    pub async fn upsert_league(&self, name: &str, display_name: &str) -> Result<i64> {
        let result = sqlx::query(
            r#"
            INSERT INTO leagues (name, display_name, is_active, first_seen, last_seen)
            VALUES (?, ?, true, datetime('now'), datetime('now'))
            ON CONFLICT(name) DO UPDATE SET
                display_name = excluded.display_name,
                last_seen = datetime('now')
            RETURNING id
            "#
        )
        .bind(name)
        .bind(display_name)
        .fetch_one(&self.pool)
        .await?;
        
        Ok(result.id)
    }
    
    // Item operations
    pub async fn search_items_fuzzy(&self, query: &str, limit: usize) -> Result<Vec<Item>> {
        // First try exact match
        let exact_matches = sqlx::query_as::<_, Item>(
            "SELECT * FROM items WHERE name LIKE ? LIMIT ?"
        )
        .bind(format!("%{}%", query))
        .bind(limit as i64)
        .fetch_all(&self.pool)
        .await?;
        
        if !exact_matches.is_empty() {
            return Ok(exact_matches);
        }
        
        // Fallback to fuzzy search
        let all_items = sqlx::query_as::<_, Item>(
            "SELECT * FROM items LIMIT 1000"
        )
        .fetch_all(&self.pool)
        .await?;
        
        let mut scored_items: Vec<(Item, i64)> = all_items
            .into_iter()
            .filter_map(|item| {
                self.fuzzy_matcher
                    .fuzzy_match(&item.name, query)
                    .map(|score| (item, score))
            })
            .collect();
        
        scored_items.sort_by(|a, b| b.1.cmp(&a.1));
        scored_items.truncate(limit);
        
        Ok(scored_items.into_iter().map(|(item, _)| item).collect())
    }
    
    pub async fn get_item_names(&self, query: &str, limit: usize) -> Result<Vec<String>> {
        let names = sqlx::query_scalar::<_, String>(
            "SELECT name FROM items WHERE name LIKE ? ORDER BY name LIMIT ?"
        )
        .bind(format!("%{}%", query))
        .bind(limit as i64)
        .fetch_all(&self.pool)
        .await?;
        
        Ok(names)
    }
    
    pub async fn upsert_item(&self, item: &PoeNinjaItem, league_id: i64, category: &str) -> Result<i64> {
        let name = item.name.as_deref().unwrap_or("Unknown");
        let details_id = item.details_id.as_deref().unwrap_or(&format!("{}_{}", category, name));
        
        let result = sqlx::query(
            r#"
            INSERT INTO items (details_id, name, type_line, base_type, category, icon_url, league_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ON CONFLICT(details_id, league_id) DO UPDATE SET
                name = excluded.name,
                type_line = excluded.type_line,
                base_type = excluded.base_type,
                icon_url = excluded.icon_url
            RETURNING id
            "#
        )
        .bind(details_id)
        .bind(name)
        .bind(&item.type_line)
        .bind(&item.base_type)
        .bind(category)
        .bind(&item.icon)
        .bind(league_id)
        .fetch_one(&self.pool)
        .await?;

        Ok(result.get::<i64, _>("id"))
    }
    
    // Price operations
    pub async fn get_latest_price(&self, item_id: i64, league: &str) -> Result<Price> {
        let price = sqlx::query_as::<_, Price>(
            r#"
            SELECT p.* FROM prices p
            JOIN leagues l ON p.league_id = l.id
            WHERE p.item_id = ? AND l.name = ?
            ORDER BY p.snapshot_time DESC
            LIMIT 1
            "#
        )
        .bind(item_id)
        .bind(league)
        .fetch_one(&self.pool)
        .await?;
        
        Ok(price)
    }
    
    pub async fn get_price_by_name(&self, item_name: &str, league: &str) -> Result<Option<Price>> {
        let price = sqlx::query_as::<_, Price>(
            r#"
            SELECT p.* FROM prices p
            JOIN items i ON p.item_id = i.id
            JOIN leagues l ON p.league_id = l.id
            WHERE i.name = ? AND l.name = ?
            ORDER BY p.snapshot_time DESC
            LIMIT 1
            "#
        )
        .bind(item_name)
        .bind(league)
        .fetch_optional(&self.pool)
        .await?;
        
        Ok(price)
    }
    
    pub async fn insert_price(&self, item_id: i64, league_id: i64, poe_item: &PoeNinjaItem) -> Result<()> {
        self.insert_price_with_confidence(item_id, league_id, poe_item, 1.0).await
    }

    pub async fn insert_price_with_confidence(&self, item_id: i64, league_id: i64, poe_item: &PoeNinjaItem, confidence: f64) -> Result<()> {
        let chaos_value = poe_item.chaos_equivalent;
        let divine_value = poe_item.divine_equivalent;
        let listing_count = poe_item.listing_count.unwrap_or(0);

        sqlx::query(
            r#"
            INSERT INTO prices (
                item_id, league_id, chaos_value, divine_value, listing_count,
                confidence_score, snapshot_time
            )
            VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
            "#
        )
        .bind(item_id)
        .bind(league_id)
        .bind(chaos_value)
        .bind(divine_value)
        .bind(listing_count)
        .bind(confidence)
        .execute(&self.pool)
        .await?;

        Ok(())
    }
    
    pub async fn get_top_traded_items(&self, limit: usize) -> Result<Vec<Item>> {
        let items = sqlx::query_as::<_, Item>(
            r#"
            SELECT DISTINCT i.* FROM items i
            JOIN prices p ON i.id = p.item_id
            WHERE p.listing_count > 10
            ORDER BY p.listing_count DESC
            LIMIT ?
            "#
        )
        .bind(limit as i64)
        .fetch_all(&self.pool)
        .await?;
        
        Ok(items)
    }
    
    // Arbitrage operations
    pub async fn get_arbitrage_opportunities(&self, budget: f64, min_profit: f64, league: &str) -> Result<Vec<FlipOpportunity>> {
        let opportunities = sqlx::query(
            r#"
            SELECT
                i.name,
                p.chaos_value as current_price,
                p.listing_count,
                p.confidence_score,
                p.liquidity_score
            FROM prices p
            JOIN items i ON p.item_id = i.id
            JOIN leagues l ON p.league_id = l.id
            WHERE l.name = ?
              AND p.chaos_value <= ?
              AND p.chaos_value > 0
              AND p.listing_count > 5
            ORDER BY p.snapshot_time DESC
            "#
        )
        .bind(league)
        .bind(budget)
        .fetch_all(&self.pool)
        .await?;
        
        let mut flips = Vec::new();
        
        for opp in opportunities {
            let current_price: Option<f64> = opp.get("current_price");
            let name: Option<String> = opp.get("name");

            if let (Some(current_price), Some(name)) = (current_price, name) {
                // Simple prediction: assume 10-20% price increase for demonstration
                let predicted_price = current_price * 1.15;
                let profit_chaos = predicted_price - current_price;
                let profit_pct = (profit_chaos / current_price) * 100.0;

                if profit_pct >= min_profit {
                    let confidence_score: Option<f64> = opp.get("confidence_score");
                    let liquidity_score: Option<f64> = opp.get("liquidity_score");
                    let listing_count: Option<i32> = opp.get("listing_count");

                    flips.push(FlipOpportunity {
                        item_name: name,
                        buy_price: current_price,
                        sell_price: predicted_price,
                        profit_chaos,
                        profit_pct,
                        risk_level: RiskLevel::Medium,
                        confidence: confidence_score.unwrap_or(0.5),
                        liquidity_score: liquidity_score.unwrap_or(0.5),
                        time_to_sell_estimate: "1-6 hours".to_string(),
                        listing_count: listing_count.unwrap_or(0),
                    });
                }
            }
        }
        
        flips.sort_by(|a, b| b.profit_pct.partial_cmp(&a.profit_pct).unwrap_or(std::cmp::Ordering::Equal));
        flips.truncate(20);
        
        Ok(flips)
    }
    
    // Additional methods for ML training
    pub async fn get_items_by_category(&self, category: &str) -> Result<Vec<Item>> {
        let items = sqlx::query_as::<_, Item>(
            "SELECT * FROM items WHERE category = ? LIMIT 100"
        )
        .bind(category)
        .fetch_all(&self.pool)
        .await?;

        Ok(items)
    }

    pub async fn get_price_history(&self, item_id: i64, days: i32) -> Result<Vec<Price>> {
        let prices = sqlx::query_as::<_, Price>(
            r#"
            SELECT * FROM prices
            WHERE item_id = ?
            AND snapshot_time >= datetime('now', '-{} days')
            ORDER BY snapshot_time ASC
            "#
        )
        .bind(item_id)
        .bind(days)
        .fetch_all(&self.pool)
        .await?;

        Ok(prices)
    }

    pub async fn get_price_statistics(&self, item_id: i64, days: i32) -> Result<PriceStatistics> {
        let stats = sqlx::query(
            r#"
            SELECT
                AVG(chaos_value) as avg_price,
                MIN(chaos_value) as min_price,
                MAX(chaos_value) as max_price,
                COUNT(*) as data_points,
                AVG(listing_count) as avg_listings
            FROM prices
            WHERE item_id = ?
            AND chaos_value IS NOT NULL
            AND snapshot_time >= datetime('now', '-{} days')
            "#
        )
        .bind(item_id)
        .bind(days)
        .fetch_one(&self.pool)
        .await?;

        Ok(PriceStatistics {
            avg_price: stats.get::<Option<f64>, _>("avg_price").unwrap_or(0.0),
            min_price: stats.get::<Option<f64>, _>("min_price").unwrap_or(0.0),
            max_price: stats.get::<Option<f64>, _>("max_price").unwrap_or(0.0),
            data_points: stats.get::<i64, _>("data_points") as usize,
            avg_listings: stats.get::<Option<f64>, _>("avg_listings").unwrap_or(0.0),
        })
    }

    pub async fn detect_price_outliers(&self, item_id: i64, threshold: f64) -> Result<Vec<Price>> {
        // Get recent prices for statistical analysis
        let recent_prices = self.get_price_history(item_id, 7).await?;

        if recent_prices.len() < 5 {
            return Ok(Vec::new());
        }

        // Calculate mean and standard deviation
        let prices: Vec<f64> = recent_prices.iter()
            .filter_map(|p| p.chaos_value)
            .collect();

        if prices.is_empty() {
            return Ok(Vec::new());
        }

        let mean = prices.iter().sum::<f64>() / prices.len() as f64;
        let variance = prices.iter()
            .map(|p| (p - mean).powi(2))
            .sum::<f64>() / prices.len() as f64;
        let std_dev = variance.sqrt();

        // Find outliers (prices beyond threshold standard deviations)
        let outliers = recent_prices.into_iter()
            .filter(|price| {
                if let Some(chaos_value) = price.chaos_value {
                    let z_score = (chaos_value - mean).abs() / std_dev;
                    z_score > threshold
                } else {
                    false
                }
            })
            .collect();

        Ok(outliers)
    }

    // Cleanup operations
    pub async fn cleanup_old_data(&self, retention_days: i64) -> Result<()> {
        sqlx::query(
            "DELETE FROM prices WHERE snapshot_time < datetime('now', '-{} days')"
        )
        .bind(retention_days)
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}

#[derive(Debug, Clone)]
pub struct PriceStatistics {
    pub avg_price: f64,
    pub min_price: f64,
    pub max_price: f64,
    pub data_points: usize,
    pub avg_listings: f64,
}

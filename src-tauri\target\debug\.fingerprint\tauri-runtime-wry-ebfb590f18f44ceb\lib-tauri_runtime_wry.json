{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 4309171813767671541, "deps": [[376837177317575824, "softbuffer", false, 7828910991885613448], [442785307232013896, "tauri_runtime", false, 2994592173974590199], [3150220818285335163, "url", false, 2358920778526070859], [3722963349756955755, "once_cell", false, 9568904435371556119], [4143744114649553716, "raw_window_handle", false, 11505498693017358128], [5986029879202738730, "log", false, 9041289895326090971], [7752760652095876438, "build_script_build", false, 8181990540654839301], [8539587424388551196, "webview2_com", false, 12310748178939823266], [9010263965687315507, "http", false, 2407541516320360975], [11050281405049894993, "tauri_utils", false, 12274861219735991302], [13223659721939363523, "tao", false, 7927026275189198606], [14585479307175734061, "windows", false, 13752367330713928517], [14794439852947137341, "wry", false, 7107514161198006087]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-ebfb590f18f44ceb\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
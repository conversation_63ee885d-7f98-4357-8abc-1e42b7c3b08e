use tauri::State;
use sqlx::Row;

use crate::{AppState, models::*};

#[tauri::command]
pub async fn get_portfolio_items(
    league: String,
    state: State<'_, AppState>,
) -> Result<Vec<PortfolioItem>, String> {
    let items = sqlx::query(
        "SELECT * FROM portfolio WHERE league = ? ORDER BY added_at DESC"
    )
    .bind(league)
    .fetch_all(&state.db_manager.get_pool())
    .await
    .map_err(|e| format!("Failed to get portfolio: {}", e))?;
    
    let mut portfolio_items = Vec::new();
    
    for item in items {
        let id: i64 = item.get("id");
        let item_name: String = item.get("item_name");
        let quantity: i32 = item.get("quantity");
        let purchase_price: f64 = item.get("purchase_price");
        let league: String = item.get("league");
        let added_at: chrono::DateTime<chrono::Utc> = item.get("added_at");

        // Get current price
        let current_value = if let Ok(Some(price)) = state.db_manager.get_price_by_name(&item_name, &league).await {
            price.chaos_value
        } else {
            None
        };

        let profit_loss = if let Some(current) = current_value {
            Some((current * quantity as f64) - (purchase_price * quantity as f64))
        } else {
            None
        };

        let profit_loss_pct = if let (Some(pl), purchase_total) = (profit_loss, purchase_price * quantity as f64) {
            if purchase_total > 0.0 {
                Some((pl / purchase_total) * 100.0)
            } else {
                None
            }
        } else {
            None
        };

        portfolio_items.push(PortfolioItem {
            id: Some(id),
            item_name,
            quantity,
            purchase_price,
            current_value,
            profit_loss,
            profit_loss_pct,
            league,
            added_at,
        });
    }
    
    Ok(portfolio_items)
}

#[tauri::command]
pub async fn add_portfolio_item(
    item: PortfolioItem,
    state: State<'_, AppState>,
) -> Result<i64, String> {
    let result = sqlx::query(
        r#"
        INSERT INTO portfolio (item_name, quantity, purchase_price, league, added_at)
        VALUES (?, ?, ?, ?, datetime('now'))
        RETURNING id
        "#
    )
    .bind(&item.item_name)
    .bind(item.quantity)
    .bind(item.purchase_price)
    .bind(&item.league)
    .fetch_one(&state.db_manager.get_pool())
    .await
    .map_err(|e| format!("Failed to add portfolio item: {}", e))?;
    
    log::info!("Added portfolio item: {} x{}", item.item_name, item.quantity);
    
    Ok(result.get::<i64, _>("id"))
}

#[tauri::command]
pub async fn remove_portfolio_item(
    id: i64,
    state: State<'_, AppState>,
) -> Result<(), String> {
    sqlx::query("DELETE FROM portfolio WHERE id = ?")
        .bind(id)
        .execute(&state.db_manager.get_pool())
        .await
        .map_err(|e| format!("Failed to remove portfolio item: {}", e))?;
    
    log::info!("Removed portfolio item with id: {}", id);
    
    Ok(())
}

#[tauri::command]
pub async fn get_portfolio_total_value(
    league: String,
    state: State<'_, AppState>,
) -> Result<f64, String> {
    let items = get_portfolio_items(league, state).await?;
    
    let total_value: f64 = items
        .iter()
        .filter_map(|item| item.current_value.map(|cv| cv * item.quantity as f64))
        .sum();
    
    Ok(total_value)
}

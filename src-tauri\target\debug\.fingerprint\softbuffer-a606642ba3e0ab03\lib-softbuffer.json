{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 3087322943372916008, "deps": [[376837177317575824, "build_script_build", false, 2704367180924539592], [4143744114649553716, "raw_window_handle", false, 11505498693017358128], [5986029879202738730, "log", false, 9041289895326090971], [10281541584571964250, "windows_sys", false, 4845814097577894739]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-a606642ba3e0ab03\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 10895048813736897673, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 12727867279189882707, "deps": [[4899080583175475170, "semver", false, 701532276892456854], [6913375703034175521, "schemars", false, 8636674945867846496], [7170110829644101142, "json_patch", false, 3149177652939400960], [9689903380558560274, "serde", false, 7414200475410930407], [11050281405049894993, "tauri_utils", false, 2151346523781693064], [12714016054753183456, "tauri_winres", false, 1651742864121187003], [13077543566650298139, "heck", false, 17322163720383359805], [13475171727366188400, "cargo_toml", false, 2559827271549073142], [13625485746686963219, "anyhow", false, 17387659364330292750], [15367738274754116744, "serde_json", false, 16572425390965055617], [15609422047640926750, "toml", false, 13186686402550205634], [15622660310229662834, "walkdir", false, 588374920433936605], [16928111194414003569, "dirs", false, 17847412115801632661], [17155886227862585100, "glob", false, 3681942166429823739]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-4ada2fd6edb00b71\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
use tokio_cron_scheduler::{JobScheduler, Job};
use std::sync::Arc;
use tokio::sync::RwLock;
use chrono::{DateTime, Utc};
use tauri::{AppHandle, Emitter};


use crate::data::DatabaseManager;
use crate::models::SyncStatus;

pub struct DataSyncScheduler {
    scheduler: JobScheduler,
    db_manager: Arc<DatabaseManager>,
    app_handle: App<PERSON>and<PERSON>,
    last_sync: Option<DateTime<Utc>>,
    is_syncing: bool,
    last_error: Option<String>,
    items_synced: u64,
}

impl DataSyncScheduler {
    pub async fn new(
        db_manager: Arc<DatabaseManager>,
        app_handle: AppHandle,
    ) -> anyhow::Result<Self> {
        let scheduler = JobScheduler::new().await?;

        Ok(Self {
            scheduler,
            db_manager,
            app_handle,
            last_sync: None,
            is_syncing: false,
            last_error: None,
            items_synced: 0,
        })
    }
    
    pub async fn start(&self) -> anyhow::Result<()> {
        let app_handle = self.app_handle.clone();

        // Schedule data sync every 31 minutes (your preferred cadence)
        let sync_job = Job::new_async("0 */31 * * * *", move |_uuid, _l| {
            let app_handle = app_handle.clone();

            Box::pin(async move {
                log::info!("Starting scheduled data sync...");

                // Emit sync started event
                let _ = app_handle.emit("sync-started", ());

                match run_rust_data_sync().await {
                    Ok(items_synced) => {
                        log::info!("Data sync completed successfully: {} items", items_synced);
                        let _ = app_handle.emit("sync-completed", items_synced);
                    }
                    Err(e) => {
                        log::error!("Data sync failed: {}", e);
                        let _ = app_handle.emit("sync-error", e.to_string());
                    }
                }
            })
        })?;

        self.scheduler.add(sync_job).await?;
        self.scheduler.start().await?;

        log::info!("Data sync scheduler started with 31-minute interval");
        Ok(())
    }
    
    pub async fn trigger_manual_sync(&mut self) -> anyhow::Result<u64> {
        log::info!("Manual data sync triggered");

        self.is_syncing = true;
        self.last_error = None;

        // Emit sync started event
        let _ = self.app_handle.emit("sync-started", ());

        match run_rust_data_sync().await {
            Ok(items_synced) => {
                log::info!("Manual sync completed: {} items", items_synced);
                self.last_sync = Some(Utc::now());
                self.items_synced = items_synced;
                self.is_syncing = false;
                let _ = self.app_handle.emit("sync-completed", items_synced);
                Ok(items_synced)
            }
            Err(e) => {
                log::error!("Manual sync failed: {}", e);
                self.last_error = Some(e.to_string());
                self.is_syncing = false;
                let _ = self.app_handle.emit("sync-error", e.to_string());
                Err(e)
            }
        }
    }

    pub fn get_sync_status(&self) -> SyncStatus {
        SyncStatus {
            is_syncing: self.is_syncing,
            last_sync: self.last_sync,
            last_error: self.last_error.clone(),
            items_synced: self.items_synced as u32,
            sync_duration_ms: 5000, // Placeholder
            next_sync_eta: self.last_sync.map(|last| last + chrono::Duration::minutes(31)),
        }
    }
}

// Function to run Rust-based data sync using our existing components
async fn run_rust_data_sync() -> anyhow::Result<u64> {
    use crate::data::{PoeNinjaClient, DatabaseManager};

    log::info!("Starting Rust-based data sync...");

    // Initialize components for data sync
    let poe_client = PoeNinjaClient::new();
    let db_manager = DatabaseManager::new(&std::env::temp_dir().join("poe_profit.db").to_string_lossy())
        .await
        .map_err(|e| anyhow::anyhow!("Failed to connect to database: {}", e))?;

    let mut total_items_synced = 0u64;

    // Default to Standard league for now
    let league = "Standard";

    // Ensure league exists in database
    let league_id = db_manager.upsert_league(league, league).await
        .map_err(|e| anyhow::anyhow!("Failed to upsert league: {}", e))?;

    // Use the existing fetch_all_data method which handles all categories
    match poe_client.fetch_all_data(league).await {
        Ok(endpoint_data_list) => {
            log::info!("Fetched data from {} endpoints", endpoint_data_list.len());

            // Process each endpoint's data
            for endpoint_data in endpoint_data_list {
                log::info!("Processing {} items from {} category",
                          endpoint_data.data.lines.len(),
                          endpoint_data.category);

                // Store items in database
                for item in &endpoint_data.data.lines {
                    match db_manager.upsert_item(item, league_id, &endpoint_data.category).await {
                        Ok(item_id) => {
                            // Insert price data
                            if let Err(e) = db_manager.insert_price(item_id, league_id, item).await {
                                log::warn!("Failed to insert price for item {}: {}", item_id, e);
                            } else {
                                total_items_synced += 1;
                            }
                        }
                        Err(e) => {
                            log::warn!("Failed to upsert item in category {}: {}", endpoint_data.category, e);
                        }
                    }
                }

                // Small delay between processing endpoints
                tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
            }
        }
        Err(e) => {
            log::error!("Failed to fetch data from poe.ninja: {}", e);
            return Err(anyhow::anyhow!("Failed to fetch data: {}", e));
        }
    }

    log::info!("Rust data sync completed: {} items synced", total_items_synced);
    Ok(total_items_synced)
}



use tokio_cron_scheduler::{JobScheduler, Job};
use std::sync::Arc;
use tokio::sync::RwLock;
use chrono::{DateTime, Utc};
use tauri::{AppHandle, Emitter};
use std::process::Command;

use crate::data::DatabaseManager;
use crate::models::SyncStatus;

pub struct DataSyncScheduler {
    scheduler: JobScheduler,
    db_manager: Arc<DatabaseManager>,
    app_handle: AppHandle,
    last_sync: Option<DateTime<Utc>>,
    is_syncing: bool,
    last_error: Option<String>,
    items_synced: u64,
}

impl DataSyncScheduler {
    pub async fn new(
        db_manager: Arc<DatabaseManager>,
        app_handle: AppHandle,
    ) -> anyhow::Result<Self> {
        let scheduler = JobScheduler::new().await?;

        Ok(Self {
            scheduler,
            db_manager,
            app_handle,
            last_sync: None,
            is_syncing: false,
            last_error: None,
            items_synced: 0,
        })
    }
    
    pub async fn start(&self) -> anyhow::Result<()> {
        let app_handle = self.app_handle.clone();

        // Schedule data sync every 31 minutes (your preferred cadence)
        let sync_job = Job::new_async("0 */31 * * * *", move |_uuid, _l| {
            let app_handle = app_handle.clone();

            Box::pin(async move {
                log::info!("Starting scheduled data sync...");

                // Emit sync started event
                let _ = app_handle.emit("sync-started", ());

                match run_python_etl_pipeline().await {
                    Ok(items_synced) => {
                        log::info!("Data sync completed successfully: {} items", items_synced);
                        let _ = app_handle.emit("sync-completed", items_synced);
                    }
                    Err(e) => {
                        log::error!("Data sync failed: {}", e);
                        let _ = app_handle.emit("sync-error", e.to_string());
                    }
                }
            })
        })?;

        self.scheduler.add(sync_job).await?;
        self.scheduler.start().await?;

        log::info!("Data sync scheduler started with 31-minute interval");
        Ok(())
    }
    
    pub async fn trigger_manual_sync(&mut self) -> anyhow::Result<u64> {
        log::info!("Manual data sync triggered");

        self.is_syncing = true;
        self.last_error = None;

        // Emit sync started event
        let _ = self.app_handle.emit("sync-started", ());

        match run_python_etl_pipeline().await {
            Ok(items_synced) => {
                log::info!("Manual sync completed: {} items", items_synced);
                self.last_sync = Some(Utc::now());
                self.items_synced = items_synced;
                self.is_syncing = false;
                let _ = self.app_handle.emit("sync-completed", items_synced);
                Ok(items_synced)
            }
            Err(e) => {
                log::error!("Manual sync failed: {}", e);
                self.last_error = Some(e.to_string());
                self.is_syncing = false;
                let _ = self.app_handle.emit("sync-error", e.to_string());
                Err(e)
            }
        }
    }

    pub fn get_sync_status(&self) -> SyncStatus {
        SyncStatus {
            is_syncing: self.is_syncing,
            last_sync: self.last_sync,
            last_error: self.last_error.clone(),
            items_synced: self.items_synced as u32,
            sync_duration_ms: 5000, // Placeholder
            next_sync_eta: self.last_sync.map(|last| last + chrono::Duration::minutes(31)),
        }
    }
}

// Function to run your existing Python ETL pipeline
async fn run_python_etl_pipeline() -> anyhow::Result<u64> {
    // Run the dedicated sync script
    let output = tokio::task::spawn_blocking(|| {
        Command::new("python")
            .arg("sync_data.py")  // Dedicated sync script
            .arg("--sync")        // Flag to run sync
            .arg("--league")      // League parameter
            .arg("Standard")      // Default league
            .output()
    }).await??;

    if output.status.success() {
        // Parse the output to get the number of items synced
        let stdout = String::from_utf8_lossy(&output.stdout);
        log::info!("Python sync output: {}", stdout);

        // Look for a line like "Synced: 1250 items"
        let items_synced = stdout
            .lines()
            .find(|line| line.contains("Synced:"))
            .and_then(|line| {
                line.split_whitespace()
                    .nth(1)
                    .and_then(|s| s.parse::<u64>().ok())
            })
            .unwrap_or(0);

        log::info!("Parsed items synced: {}", items_synced);
        Ok(items_synced)
    } else {
        let stderr = String::from_utf8_lossy(&output.stderr);
        let stdout = String::from_utf8_lossy(&output.stdout);
        log::error!("Python sync failed - stderr: {}", stderr);
        log::error!("Python sync failed - stdout: {}", stdout);
        Err(anyhow::anyhow!("Python ETL pipeline failed: {}", stderr))
    }
}



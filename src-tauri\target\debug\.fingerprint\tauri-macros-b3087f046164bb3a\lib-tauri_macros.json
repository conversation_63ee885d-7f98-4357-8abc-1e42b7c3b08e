{"rustc": 10895048813736897673, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 4890042416445576381, "deps": [[3060637413840920116, "proc_macro2", false, 11906168919180298248], [7341521034400937459, "tauri_codegen", false, 18372016940673005633], [10640660562325816595, "syn", false, 13889071688505615844], [11050281405049894993, "tauri_utils", false, 2151346523781693064], [13077543566650298139, "heck", false, 17322163720383359805], [17990358020177143287, "quote", false, 299949230227233179]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-b3087f046164bb3a\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
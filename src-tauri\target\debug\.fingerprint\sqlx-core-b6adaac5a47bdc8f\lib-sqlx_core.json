{"rustc": 10895048813736897673, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 15657897354478470176, "path": 15405186509753581741, "deps": [[5103565458935487, "futures_io", false, 2916238768400003225], [40386456601120721, "percent_encoding", false, 10260631236887431270], [530211389790465181, "hex", false, 8631564807700701099], [788558663644978524, "crossbeam_queue", false, 4894750534075121333], [966925859616469517, "ahash", false, 7521342665936769189], [1162433738665300155, "crc", false, 15346441757005831096], [1464803193346256239, "event_listener", false, 18015725679060578978], [1811549171721445101, "futures_channel", false, 10301335226058481924], [3150220818285335163, "url", false, 9500151397969033235], [3405817021026194662, "hashlink", false, 7994913174547675926], [3646857438214563691, "futures_intrusive", false, 3460948478823686235], [3666196340704888985, "smallvec", false, 9943696398174068305], [3712811570531045576, "byteorder", false, 3493597116683261420], [3722963349756955755, "once_cell", false, 9568904435371556119], [5986029879202738730, "log", false, 16003787354280930940], [7620660491849607393, "futures_core", false, 9691891082847061150], [8008191657135824715, "thiserror", false, 11727692953745943051], [8319709847752024821, "uuid", false, 2465285060543535506], [8606274917505247608, "tracing", false, 10745432318678643702], [9538054652646069845, "tokio", false, 15498150212556191890], [9689903380558560274, "serde", false, 7414200475410930407], [9857275760291862238, "sha2", false, 6123675264601303057], [9897246384292347999, "chrono", false, 5976950063823359705], [10629569228670356391, "futures_util", false, 1330627506576143502], [10862088793507253106, "sqlformat", false, 12208467275781168444], [11295624341523567602, "rustls", false, 3570026737871439464], [12170264697963848012, "either", false, 15324103172536635554], [14483812548788871374, "indexmap", false, 1700885372328588936], [15367738274754116744, "serde_json", false, 16572425390965055617], [15932120279885307830, "memchr", false, 9098527244056309973], [16066129441945555748, "bytes", false, 33194868233359760], [16311359161338405624, "rustls_pemfile", false, 2540710727512137887], [16973251432615581304, "tokio_stream", false, 1862379405550372188], [17106256174509013259, "atoi", false, 10226638350684090366], [17605717126308396068, "paste", false, 764143708312869916], [17652733826348741533, "webpki_roots", false, 15876523349470295877]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-core-b6adaac5a47bdc8f\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
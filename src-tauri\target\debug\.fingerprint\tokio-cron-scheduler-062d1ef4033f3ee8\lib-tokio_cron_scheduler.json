{"rustc": 10895048813736897673, "features": "[\"default\"]", "declared_features": "[\"default\", \"has_bytes\", \"log\", \"nats\", \"nats_storage\", \"postgres-native-tls\", \"postgres-openssl\", \"postgres_native_tls\", \"postgres_openssl\", \"postgres_storage\", \"prost\", \"prost-build\", \"signal\", \"tokio-postgres\", \"tracing-subscriber\"]", "target": 2318398449392625286, "profile": 15657897354478470176, "path": 17847704700152913149, "deps": [[5157631553186200874, "num_traits", false, 8250564751257972305], [5990956534088275425, "num_derive", false, 17938953621008115746], [7294361402007043243, "cron", false, 8090600606006142118], [8319709847752024821, "uuid", false, 2465285060543535506], [8606274917505247608, "tracing", false, 2529322788328241119], [9538054652646069845, "tokio", false, 13698903167287799090], [9897246384292347999, "chrono", false, 12453371447487064412], [12555647011536806788, "build_script_build", false, 14038531325890882723]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tokio-cron-scheduler-062d1ef4033f3ee8\\dep-lib-tokio_cron_scheduler", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 10895048813736897673, "features": "[\"__rustls\", \"__tls\", \"default\", \"default-tls\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-native-tls\", \"tokio-rustls\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 15657897354478470176, "path": 12088122433996507385, "deps": [[40386456601120721, "percent_encoding", false, 10260631236887431270], [95042085696191081, "ipnet", false, 4199129472770704267], [264090853244900308, "sync_wrapper", false, 12430366729117495869], [784494742817713399, "tower_service", false, 18056485729894558669], [1044435446100926395, "hyper_rustls", false, 11086434210035566850], [1906322745568073236, "pin_project_lite", false, 1254142009610365093], [3150220818285335163, "url", false, 2358920778526070859], [3722963349756955755, "once_cell", false, 9568904435371556119], [4405182208873388884, "http", false, 8896462972149056940], [5986029879202738730, "log", false, 9041289895326090971], [7414427314941361239, "hyper", false, 1215365293889688969], [7620660491849607393, "futures_core", false, 9691891082847061150], [8405603588346937335, "winreg", false, 17622688024202782530], [8915503303801890683, "http_body", false, 15829190409768435822], [9538054652646069845, "tokio", false, 13698903167287799090], [9689903380558560274, "serde", false, 7414200475410930407], [10229185211513642314, "mime", false, 4599952621902664255], [10629569228670356391, "futures_util", false, 14060607619919236999], [11295624341523567602, "rustls", false, 15924722707480759115], [12186126227181294540, "tokio_native_tls", false, 4916097949771884948], [12367227501898450486, "hyper_tls", false, 7026216466652058488], [13809605890706463735, "h2", false, 1758577729209111061], [14564311161534545801, "encoding_rs", false, 10562231735299819085], [15367738274754116744, "serde_json", false, 15399624556204434903], [16066129441945555748, "bytes", false, 33194868233359760], [16311359161338405624, "rustls_pemfile", false, 2540710727512137887], [16542808166767769916, "serde_urlencoded", false, 17741780201827547549], [16622232390123975175, "tokio_rustls", false, 4377349778991985753], [16785601910559813697, "native_tls_crate", false, 15590042305135114874], [17652733826348741533, "webpki_roots", false, 15876523349470295877], [18066890886671768183, "base64", false, 3142928587311103600]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-e015567f93200bff\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
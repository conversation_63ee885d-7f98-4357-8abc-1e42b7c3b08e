{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 2397774075986403827, "deps": [[442785307232013896, "build_script_build", false, 15805198089873842148], [3150220818285335163, "url", false, 2358920778526070859], [4143744114649553716, "raw_window_handle", false, 11505498693017358128], [7606335748176206944, "dpi", false, 7905489634539449184], [9010263965687315507, "http", false, 2407541516320360975], [9689903380558560274, "serde", false, 7414200475410930407], [10806645703491011684, "thiserror", false, 2421778471660001397], [11050281405049894993, "tauri_utils", false, 12274861219735991302], [14585479307175734061, "windows", false, 13752367330713928517], [15367738274754116744, "serde_json", false, 15399624556204434903], [16727543399706004146, "cookie", false, 2266377093931584693]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-0cbda8c391a05564\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
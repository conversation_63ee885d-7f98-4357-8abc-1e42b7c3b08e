{"rustc": 10895048813736897673, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 5096884971392396598, "deps": [[3060637413840920116, "proc_macro2", false, 11906168919180298248], [3150220818285335163, "url", false, 9500151397969033235], [4899080583175475170, "semver", false, 701532276892456854], [7170110829644101142, "json_patch", false, 3149177652939400960], [7392050791754369441, "ico", false, 16670098271968004737], [8319709847752024821, "uuid", false, 2465285060543535506], [9689903380558560274, "serde", false, 7414200475410930407], [9857275760291862238, "sha2", false, 6123675264601303057], [10640660562325816595, "syn", false, 13889071688505615844], [10806645703491011684, "thiserror", false, 2421778471660001397], [11050281405049894993, "tauri_utils", false, 2151346523781693064], [12687914511023397207, "png", false, 6621171150746547413], [13077212702700853852, "base64", false, 5717140456236041030], [14132538657330703225, "brotli", false, 13183974194630361403], [15367738274754116744, "serde_json", false, 16572425390965055617], [15622660310229662834, "walkdir", false, 588374920433936605], [17990358020177143287, "quote", false, 299949230227233179]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-3eab82f4cedb1e56\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
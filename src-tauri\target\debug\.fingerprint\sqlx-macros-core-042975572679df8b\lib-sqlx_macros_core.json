{"rustc": 10895048813736897673, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"postgres\", \"sqlx-postgres\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 2225463790103693989, "path": 13382627114906810621, "deps": [[530211389790465181, "hex", false, 8631564807700701099], [996810380461694889, "sqlx_core", false, 18070660541545428214], [1441306149310335789, "tempfile", false, 5449109803056225688], [2713742371683562785, "syn", false, 17375468584397320090], [3060637413840920116, "proc_macro2", false, 11906168919180298248], [3150220818285335163, "url", false, 9500151397969033235], [3405707034081185165, "dotenvy", false, 25551874139072657], [3722963349756955755, "once_cell", false, 9568904435371556119], [8045585743974080694, "heck", false, 5388992003344237375], [9538054652646069845, "tokio", false, 15498150212556191890], [9689903380558560274, "serde", false, 7414200475410930407], [9857275760291862238, "sha2", false, 6123675264601303057], [12170264697963848012, "either", false, 15324103172536635554], [15367738274754116744, "serde_json", false, 16572425390965055617], [15634168271133386882, "sqlx_postgres", false, 10351944542684908497], [17990358020177143287, "quote", false, 299949230227233179]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-core-042975572679df8b\\dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::sync::Arc;
use std::time::Instant;
use tokio::sync::RwLock;
use tauri::{State, Manager};

mod models;
mod data;
mod commands;
mod cache;
mod scheduler;

use models::*;
use data::{DatabaseManager, PoeNinjaClient, DataValidator};
use cache::CacheManager;
use scheduler::DataSyncScheduler;

// Application state
#[derive(Clone)]
pub struct AppState {
    pub db_manager: Arc<DatabaseManager>,
    pub poe_client: Arc<PoeNinjaClient>,
    pub data_validator: Arc<DataValidator>,
    pub cache_manager: Arc<RwLock<CacheManager>>,
    pub sync_scheduler: Arc<RwLock<Option<DataSyncScheduler>>>,
    pub startup_time: Instant,
}



// Import commands from separate modules
use commands::*;







#[tokio::main]
async fn main() {
    // Initialize logging
    env_logger::init();

    // Initialize application state
    let startup_time = Instant::now();

    // Initialize database
    let db_path = "data/poe_profit.db";
    let db_manager = Arc::new(
        DatabaseManager::new(db_path)
            .await
            .expect("Failed to initialize database")
    );

    // Initialize PoE.ninja client
    let poe_client = Arc::new(PoeNinjaClient::new());

    // Initialize data validator
    let data_validator = Arc::new(DataValidator::default());

    // Initialize cache manager
    let cache_manager = Arc::new(RwLock::new(CacheManager::new()));

    // Create placeholder for sync scheduler (will be initialized in setup)
    let sync_scheduler = Arc::new(RwLock::new(None::<DataSyncScheduler>));

    let app_state = AppState {
        db_manager: db_manager.clone(),
        poe_client,
        data_validator,
        cache_manager,
        sync_scheduler: sync_scheduler.clone(),
        startup_time,
    };

    tauri::Builder::default()
        .manage(app_state)
        .setup(move |app| {
            // Initialize sync scheduler with proper app handle
            let app_handle = app.handle().clone();
            let db_manager_clone = db_manager.clone();
            let sync_scheduler_clone = sync_scheduler.clone();

            tauri::async_runtime::spawn(async move {
                match DataSyncScheduler::new(db_manager_clone, app_handle).await {
                    Ok(mut scheduler) => {
                        log::info!("Starting background sync scheduler...");
                        if let Err(e) = scheduler.start().await {
                            log::error!("Failed to start sync scheduler: {}", e);
                        }
                        *sync_scheduler_clone.write().await = Some(scheduler);
                    }
                    Err(e) => {
                        log::error!("Failed to initialize sync scheduler: {}", e);
                    }
                }
            });
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            get_top_flip_opportunities,
            get_league_list,
            get_app_info,
            get_sync_status,
            get_portfolio_total_value,
            get_market_summary,
            force_data_sync,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}



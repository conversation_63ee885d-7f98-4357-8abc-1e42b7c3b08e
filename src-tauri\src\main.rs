// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use serde::{Deserialize, Serialize};

// Simplified models for basic functionality
#[derive(Debug, Serialize, Deserialize)]
pub struct FlipOpportunity {
    pub item_name: String,
    pub buy_price: f64,
    pub sell_price: f64,
    pub profit_chaos: f64,
    pub profit_pct: f64,
    pub risk_level: String,
    pub confidence: f64,
    pub liquidity_score: f64,
    pub time_to_sell_estimate: String,
    pub listing_count: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct League {
    pub id: i32,
    pub name: String,
    pub display_name: String,
    pub is_active: bool,
    pub first_seen: String,
    pub last_seen: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AppInfo {
    pub version: String,
    pub uptime: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SyncStatus {
    pub is_syncing: bool,
    pub last_sync: Option<String>,
    pub last_error: Option<String>,
    pub items_synced: u64,
    pub sync_duration_ms: u64,
    pub next_sync_eta: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MarketSummary {
    pub total_items: u64,
    pub active_leagues: u64,
    pub last_update: String,
    pub top_movers: Vec<TopMover>,
    pub currency_rates: CurrencyRates,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TopMover {
    pub item_name: String,
    pub change_pct: f64,
    pub current_price: f64,
    pub volume: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CurrencyRates {
    pub chaos_to_divine: f64,
    pub divine_to_chaos: f64,
    pub exalt_to_chaos: Option<f64>,
}



// Simplified Tauri commands for basic functionality
#[tauri::command]
async fn get_top_flip_opportunities(
    league: Option<String>,
    limit: Option<usize>
) -> Result<Vec<FlipOpportunity>, String> {
    let _league = league.unwrap_or_else(|| "Standard".to_string());
    let limit = limit.unwrap_or(10);

    // Return mock data for now - will be replaced with real database integration
    let mut opportunities = vec![
        FlipOpportunity {
            item_name: "Chaos Orb".to_string(),
            buy_price: 1.0,
            sell_price: 1.2,
            profit_chaos: 0.2,
            profit_pct: 20.0,
            risk_level: "Low".to_string(),
            confidence: 0.85,
            liquidity_score: 0.9,
            time_to_sell_estimate: "< 1 hour".to_string(),
            listing_count: 150,
        },
        FlipOpportunity {
            item_name: "Divine Orb".to_string(),
            buy_price: 200.0,
            sell_price: 220.0,
            profit_chaos: 20.0,
            profit_pct: 10.0,
            risk_level: "Medium".to_string(),
            confidence: 0.92,
            liquidity_score: 0.75,
            time_to_sell_estimate: "2-4 hours".to_string(),
            listing_count: 45,
        },
        FlipOpportunity {
            item_name: "Exalted Orb".to_string(),
            buy_price: 15.0,
            sell_price: 17.5,
            profit_chaos: 2.5,
            profit_pct: 16.7,
            risk_level: "Low".to_string(),
            confidence: 0.78,
            liquidity_score: 0.65,
            time_to_sell_estimate: "1-2 hours".to_string(),
            listing_count: 89,
        },
    ];

    opportunities.truncate(limit);
    Ok(opportunities)
}

#[tauri::command]
async fn get_league_list() -> Result<Vec<League>, String> {
    // Return mock data for now - will be replaced with real database integration
    Ok(vec![
        League {
            id: 1,
            name: "Standard".to_string(),
            display_name: "Standard".to_string(),
            is_active: true,
            first_seen: "2025-01-01T00:00:00Z".to_string(),
            last_seen: "2025-01-15T12:00:00Z".to_string(),
        },
        League {
            id: 2,
            name: "Hardcore".to_string(),
            display_name: "Hardcore".to_string(),
            is_active: true,
            first_seen: "2025-01-01T00:00:00Z".to_string(),
            last_seen: "2025-01-15T12:00:00Z".to_string(),
        },
        League {
            id: 3,
            name: "Settlers".to_string(),
            display_name: "Settlers of Kalguur".to_string(),
            is_active: true,
            first_seen: "2025-01-01T00:00:00Z".to_string(),
            last_seen: "2025-01-15T12:00:00Z".to_string(),
        },
    ])
}

#[tauri::command]
async fn get_app_info() -> Result<AppInfo, String> {
    Ok(AppInfo {
        version: "1.0.0".to_string(),
        uptime: 0, // Will be calculated properly later
    })
}

#[tauri::command]
async fn get_sync_status() -> Result<SyncStatus, String> {
    // Return mock status for now - will be replaced with real database integration
    Ok(SyncStatus {
        is_syncing: false,
        last_sync: Some("2025-01-15T10:30:00Z".to_string()),
        last_error: None,
        items_synced: 1250,
        sync_duration_ms: 5000,
        next_sync_eta: Some("2025-01-15T11:00:00Z".to_string()),
    })
}

#[tauri::command]
async fn get_portfolio_total_value(league: String) -> Result<f64, String> {
    // Return mock value for now - will be replaced with real database integration
    let _league = league; // Use the league parameter in real implementation
    Ok(1250.75) // Mock value in chaos orbs
}

#[tauri::command]
async fn get_market_summary(league: String) -> Result<MarketSummary, String> {
    // Return mock summary for now - will be replaced with real database integration
    let _league = league; // Use the league parameter in real implementation
    Ok(MarketSummary {
        total_items: 15420,
        active_leagues: 3,
        last_update: "2025-01-15T12:00:00Z".to_string(),
        top_movers: vec![
            TopMover {
                item_name: "Divine Orb".to_string(),
                change_pct: 5.2,
                current_price: 220.0,
                volume: 1250,
            },
            TopMover {
                item_name: "Chaos Orb".to_string(),
                change_pct: -2.1,
                current_price: 1.0,
                volume: 8900,
            },
            TopMover {
                item_name: "Exalted Orb".to_string(),
                change_pct: 1.8,
                current_price: 16.5,
                volume: 450,
            },
        ],
        currency_rates: CurrencyRates {
            chaos_to_divine: 0.0045,
            divine_to_chaos: 220.0,
            exalt_to_chaos: Some(16.5),
        },
    })
}

#[tauri::command]
async fn force_data_sync() -> Result<String, String> {
    // Mock sync for now - will be replaced with real implementation
    Ok("Sync completed: 1250 items".to_string())
}

#[tokio::main]
async fn main() {
    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![
            get_top_flip_opportunities,
            get_league_list,
            get_app_info,
            get_sync_status,
            get_portfolio_total_value,
            get_market_summary,
            force_data_sync,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}



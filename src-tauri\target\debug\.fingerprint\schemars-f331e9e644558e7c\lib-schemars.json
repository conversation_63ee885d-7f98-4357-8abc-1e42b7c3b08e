{"rustc": 10895048813736897673, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 5542233668750092718, "deps": [[3150220818285335163, "url", false, 9500151397969033235], [6913375703034175521, "build_script_build", false, 1525606034418345024], [8319709847752024821, "uuid1", false, 2465285060543535506], [9122563107207267705, "dyn_clone", false, 16997414196527766932], [9689903380558560274, "serde", false, 7414200475410930407], [14923790796823607459, "indexmap", false, 16269835957256239798], [15367738274754116744, "serde_json", false, 16572425390965055617], [16071897500792579091, "schemars_derive", false, 1605963841565082053]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-f331e9e644558e7c\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}